import logging

class BaseAction:
    """Base class for all action handlers"""

    def __init__(self, controller=None):
        """
        Initialize the action handler

        Args:
            controller: The device controller to use for actions
        """
        self.controller = controller
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize method selector for strategic method selection
        try:
            from ..utils.method_selector import method_selector
            self.method_selector = method_selector
        except ImportError:
            # Fallback if import fails
            self.method_selector = None
            self.logger.warning("Method selector not available, using default method selection")

    def execute(self, params):
        """
        Execute the action with the given parameters

        Args:
            params: Dictionary of parameters for the action

        Returns:
            dict: Result of the action with status and message
        """
        raise NotImplementedError("Subclasses must implement execute method")

    def set_controller(self, controller):
        """
        Update the controller for the action handler

        Args:
            controller: The device controller to use
        """
        self.controller = controller

    def get_global_timeout(self, default=60):
        """
        Get the global element timeout value from the database

        Args:
            default: Default timeout value if global setting can't be retrieved

        Returns:
            int: The global timeout value in seconds
        """
        try:
            try:
                from ..utils.global_values_db import GlobalValuesDB
            except ImportError:
                import sys
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                from app_android.utils.global_values_db import GlobalValuesDB
            global_values_db = GlobalValuesDB()
            timeout = global_values_db.get_value('default_element_timeout', default)
            return int(timeout)
        except Exception as e:
            self.logger.warning(f"Could not get default timeout from global settings: {e}")
            return default

    def take_screenshot_after_action(self):
        """Take a screenshot after action execution if controller supports it"""
        if not self.controller:
            return None

        try:
            screenshot_path = self.controller.take_screenshot()
            return screenshot_path
        except Exception as e:
            self.logger.error(f"Error taking screenshot after action: {e}")
            return None

    def cleanup_debug_images(self):
        """Clean up debug images created during action execution"""
        try:
            # Clean up debug images from the controller's image matcher if available
            if hasattr(self.controller, 'image_matcher') and self.controller.image_matcher:
                self.controller.image_matcher.cleanup_debug_images()
                self.logger.debug("Cleaned up debug images from image matcher")

            # Also clean up any debug images from temp directory
            from ..utils.file_utils import cleanup_temp_files
            cleanup_temp_files(max_age_hours=1)  # Clean files older than 1 hour

        except Exception as e:
            self.logger.warning(f"Error during debug image cleanup: {e}")

    def execute_with_cleanup(self, params):
        """
        Execute the action with automatic debug image cleanup

        Args:
            params: Dictionary of parameters for the action

        Returns:
            dict: Result of the action with status and message
        """
        try:
            # Execute the main action
            result = self.execute(params)
            return result
        finally:
            # Always clean up debug images after action execution
            self.cleanup_debug_images()

    def execute_with_retry(self, operation_func, max_retries=3, retry_delay=2, operation_name="operation"):
        """
        Execute an operation with retry mechanism for better stability

        Args:
            operation_func: Function to execute
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
            operation_name: Name of the operation for logging

        Returns:
            Result of the operation or error dict
        """
        import time
        from requests.exceptions import ConnectionError, ReadTimeout
        from selenium.common.exceptions import WebDriverException

        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"Retry attempt {attempt}/{max_retries} for {operation_name}")
                    time.sleep(retry_delay)

                result = operation_func()

                if attempt > 0:
                    self.logger.info(f"{operation_name} succeeded on retry attempt {attempt}")

                return result

            except (ConnectionError, ReadTimeout, WebDriverException) as e:
                last_exception = e
                error_msg = str(e)

                # Check for specific timeout errors
                if "Read timed out" in error_msg or "HTTPConnectionPool" in error_msg:
                    self.logger.warning(f"{operation_name} failed with timeout error (attempt {attempt + 1}/{max_retries + 1}): {error_msg}")

                    # Try to reconnect if controller supports it
                    if hasattr(self.controller, 'reconnect_if_needed'):
                        try:
                            self.controller.reconnect_if_needed()
                        except Exception as reconnect_e:
                            self.logger.warning(f"Reconnection attempt failed: {reconnect_e}")
                else:
                    self.logger.warning(f"{operation_name} failed (attempt {attempt + 1}/{max_retries + 1}): {error_msg}")

                if attempt == max_retries:
                    break

            except Exception as e:
                # For non-retryable exceptions, fail immediately
                self.logger.error(f"{operation_name} failed with non-retryable error: {e}")
                return {
                    "status": "error",
                    "message": f"{operation_name} failed: {str(e)}"
                }

        # All retries exhausted
        return {
            "status": "error",
            "message": f"{operation_name} failed after {max_retries + 1} attempts. Last error: {str(last_exception)}"
        }

    def should_use_airtest_for_action(self, action_type, operation_type=None):
        """
        Determine if AirTest should be used for this action

        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action (optional)

        Returns:
            bool: True if AirTest should be used, False otherwise
        """
        if self.method_selector:
            return self.method_selector.should_use_airtest(action_type, operation_type)

        # Fallback logic if method selector is not available
        # Only use AirTest for explicit text/image operations
        airtest_operations = ['text_recognition', 'image_recognition', 'find_text', 'find_image']
        return operation_type in airtest_operations if operation_type else False

    def get_preferred_automation_method(self, action_type, operation_type=None, available_methods=None):
        """
        Get the preferred automation method for this action

        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action (optional)
            available_methods (list): List of available methods

        Returns:
            str: The preferred method to use
        """
        if self.method_selector:
            return self.method_selector.get_preferred_method(action_type, operation_type, available_methods)

        # Fallback logic
        if available_methods is None:
            available_methods = ['uiautomator', 'appium', 'adb']

        return available_methods[0] if available_methods else 'uiautomator'

    def log_method_selection(self, action_type, operation_type, selected_method, reason=""):
        """
        Log the method selection decision

        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action
            selected_method (str): The method that was selected
            reason (str): Additional reason for the selection
        """
        if self.method_selector:
            self.method_selector.log_method_selection(action_type, operation_type, selected_method, reason)
        else:
            self.logger.info(f"Method Selection: {action_type}/{operation_type} -> {selected_method} ({reason})")

    def scale_ios_coordinates(self, coordinates, device_info=None):
        """
        Scale coordinates for iOS devices to match the UI scale

        This implements similar scaling logic to the client-side JavaScript:
        const scaleX = this.deviceScreen.naturalWidth / rect.width;
        const scaleY = this.deviceScreen.naturalHeight / rect.height;

        Args:
            coordinates: Tuple of (x, y) coordinates to scale
            device_info: Optional device info to use for scaling factors

        Returns:
            Tuple of scaled (x, y) coordinates
        """
        from airtest.core.helper import G

        # If not iOS or no coordinates, return as is
        if not hasattr(self.controller, 'platform_name') or self.controller.platform_name != 'iOS':
            return coordinates

        if not coordinates or len(coordinates) != 2:
            return coordinates

        # Get original coordinates
        original_x, original_y = coordinates

        # Try to get device dimensions from controller
        device_dimensions = None
        if hasattr(self.controller, 'get_device_dimensions'):
            try:
                device_dimensions = self.controller.get_device_dimensions()
                self.logger.info(f"Device physical dimensions: {device_dimensions}")
            except Exception as dim_err:
                self.logger.warning(f"Could not get device dimensions: {dim_err}")

        # Try to get UI dimensions from Airtest
        ui_dimensions = None
        try:
            if hasattr(G, 'DEVICE') and G.DEVICE and hasattr(G.DEVICE, 'display_info'):
                display_info = G.DEVICE.display_info
                if display_info and 'width' in display_info and 'height' in display_info:
                    ui_dimensions = (display_info['width'], display_info['height'])
                    self.logger.info(f"Device UI dimensions: {ui_dimensions}")
        except Exception as e:
            self.logger.warning(f"Could not get Airtest display info: {e}")

        # If we have both dimensions, calculate actual scaling factor
        if device_dimensions and ui_dimensions and device_dimensions[0] > 0 and device_dimensions[1] > 0:
            # This matches the client-side scaling logic
            scale_x = ui_dimensions[0] / device_dimensions[0]
            scale_y = ui_dimensions[1] / device_dimensions[1]

            scaled_x = int(original_x * scale_x)
            scaled_y = int(original_y * scale_y)

            self.logger.info(f"iOS coordinate scaling: ({original_x}, {original_y}) -> ({scaled_x}, {scaled_y})")
            return (scaled_x, scaled_y)
        else:
            # Apply a fixed scaling factor based on the examples given
            # Appears to be approximately 1/3 (from 651->214, 2623->876)
            scale_factor = 0.33

            scaled_x = int(original_x * scale_factor)
            scaled_y = int(original_y * scale_factor)

            self.logger.info(f"iOS coordinate scaling (fixed): ({original_x}, {original_y}) -> ({scaled_x}, {scaled_y})")
            return (scaled_x, scaled_y)

    def find_element_with_locator(self, locator_type, locator_value, timeout=None):
        """
        Universal method to find elements using various locator types including UISelector

        Args:
            locator_type: Type of locator (id, xpath, accessibility_id, uiselector, etc.)
            locator_value: Value of the locator
            timeout: Optional timeout for element finding

        Returns:
            WebElement or None if not found
        """
        if not self.controller or not hasattr(self.controller, 'driver') or not self.controller.driver:
            self.logger.error("No Appium driver available")
            return None

        timeout = timeout or self.get_global_timeout()

        # Ensure minimum timeout for Android stability
        if timeout < 10:
            timeout = 10
            self.logger.info(f"Increased timeout to {timeout} seconds for Android stability")

        # Retry mechanism for better reliability
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                self.logger.info(f"Finding element attempt {attempt + 1}/{max_retries}: {locator_type}='{locator_value}'")

                # Handle UISelector locator type
                if locator_type.lower() == 'uiselector':
                    element = self._find_element_by_uiselector(locator_value, timeout)
                else:
                    # Handle standard locator types
                    element = self._find_element_by_standard_locator(locator_type, locator_value, timeout)

                if element:
                    self.logger.info(f"Element found successfully on attempt {attempt + 1}")
                    return element

            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")

                # If this is not the last attempt, wait before retrying
                if attempt < max_retries - 1:
                    import time
                    self.logger.info(f"Waiting {retry_delay} seconds before retry...")
                    time.sleep(retry_delay)
                else:
                    self.logger.error(f"All {max_retries} attempts failed for {locator_type}='{locator_value}'")

        return None

    def _find_element_by_uiselector(self, uiselector_value, timeout):
        """
        Find element using UISelector (Android UIAutomator)

        Args:
            uiselector_value: UISelector string
            timeout: Timeout for element finding

        Returns:
            WebElement or None if not found
        """
        try:
            # Import AppiumBy for proper UIAutomator2 usage
            try:
                from appium.webdriver.common.appiumby import AppiumBy
                locator_type = AppiumBy.ANDROID_UIAUTOMATOR
            except ImportError:
                # Fallback to MobileBy for older Appium versions
                from appium.webdriver.common.mobileby import MobileBy
                locator_type = MobileBy.ANDROID_UIAUTOMATOR

            # Use WebDriverWait for better reliability
            if self._has_selenium_support():
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                wait = WebDriverWait(self.controller.driver, timeout)
                element = wait.until(EC.presence_of_element_located((locator_type, uiselector_value)))
                return element
            else:
                # Direct find without wait
                return self.controller.driver.find_element(locator_type, uiselector_value)

        except Exception as e:
            self.logger.warning(f"UISelector element finding failed: {e}")
            return None

    def _find_element_by_standard_locator(self, locator_type, locator_value, timeout):
        """
        Find element using standard locator types

        Args:
            locator_type: Standard locator type (id, xpath, accessibility_id, etc.)
            locator_value: Locator value
            timeout: Timeout for element finding

        Returns:
            WebElement or None if not found
        """
        try:
            # Use AppiumBy for proper Android/iOS locator handling
            if self._has_selenium_support():
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                # Import AppiumBy for proper mobile locator handling
                try:
                    from appium.webdriver.common.appiumby import AppiumBy

                    locator_map = {
                        'id': AppiumBy.ID,  # Properly handles Android resource-id
                        'xpath': AppiumBy.XPATH,
                        'name': AppiumBy.NAME,
                        'class': AppiumBy.CLASS_NAME,
                        'class_name': AppiumBy.CLASS_NAME,
                        'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                        'android_uiautomator': AppiumBy.ANDROID_UIAUTOMATOR,
                        'ios_predicate': AppiumBy.IOS_PREDICATE,
                        'ios_class_chain': AppiumBy.IOS_CLASS_CHAIN,
                        'css_selector': AppiumBy.CSS_SELECTOR,
                        'link_text': AppiumBy.LINK_TEXT,
                        'partial_link_text': AppiumBy.PARTIAL_LINK_TEXT,
                        'tag_name': AppiumBy.TAG_NAME
                    }
                except ImportError:
                    # Fallback to older MobileBy for compatibility
                    from appium.webdriver.common.mobileby import MobileBy
                    from selenium.webdriver.common.by import By

                    locator_map = {
                        'id': MobileBy.ID,
                        'xpath': By.XPATH,
                        'name': By.NAME,
                        'class': By.CLASS_NAME,
                        'class_name': By.CLASS_NAME,
                        'accessibility_id': MobileBy.ACCESSIBILITY_ID,
                        'android_uiautomator': MobileBy.ANDROID_UIAUTOMATOR,
                        'ios_predicate': MobileBy.IOS_PREDICATE,
                        'ios_class_chain': MobileBy.IOS_CLASS_CHAIN
                    }

                by_type = locator_map.get(locator_type.lower())
                if not by_type:
                    # Default to xpath if locator type not found
                    by_type = locator_map.get('xpath', AppiumBy.XPATH if 'AppiumBy' in locals() else By.XPATH)
                    self.logger.warning(f"Unknown locator type '{locator_type}', using XPath as fallback")

                wait = WebDriverWait(self.controller.driver, timeout)
                element = wait.until(EC.presence_of_element_located((by_type, locator_value)))
                return element
            else:
                # Direct find without wait - fallback for older versions
                # Use modern find_element method with AppiumBy
                try:
                    from appium.webdriver.common.appiumby import AppiumBy

                    locator_map = {
                        'id': AppiumBy.ID,
                        'xpath': AppiumBy.XPATH,
                        'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                        'class': AppiumBy.CLASS_NAME,
                        'class_name': AppiumBy.CLASS_NAME,
                        'name': AppiumBy.NAME
                    }

                    by_type = locator_map.get(locator_type.lower())
                    if by_type:
                        return self.controller.driver.find_element(by_type, locator_value)
                    else:
                        self.logger.warning(f"Unsupported locator type for fallback: {locator_type}")
                        return None

                except ImportError:
                    # Very old fallback using deprecated methods
                    if locator_type.lower() == 'id':
                        return self.controller.driver.find_element_by_id(locator_value)
                    elif locator_type.lower() == 'xpath':
                        return self.controller.driver.find_element_by_xpath(locator_value)
                    elif locator_type.lower() == 'accessibility_id':
                        return self.controller.driver.find_element_by_accessibility_id(locator_value)
                    elif locator_type.lower() in ['class', 'class_name']:
                        return self.controller.driver.find_element_by_class_name(locator_value)
                    elif locator_type.lower() == 'name':
                        return self.controller.driver.find_element_by_name(locator_value)
                    else:
                        self.logger.warning(f"Unsupported locator type for fallback: {locator_type}")
                        return None

        except Exception as e:
            # Provide detailed error information for debugging
            error_msg = f"Standard locator element finding failed for {locator_type}='{locator_value}': {e}"

            # Add specific guidance for common Android ID locator issues
            if locator_type.lower() == 'id':
                error_msg += f"\n  Hint: For Android, ensure the ID value is the full resource-id (e.g., 'com.app:id/button')"
                error_msg += f"\n  Hint: You can also try using 'accessibility_id' locator type instead"
                error_msg += f"\n  Hint: Consider using UI Selector: new UiSelector().resourceId('{locator_value}')"

            self.logger.warning(error_msg)
            return None

    def _has_selenium_support(self):
        """Check if Selenium WebDriverWait is available"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            return True
        except ImportError:
            return False