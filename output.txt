2025-07-07 16:33:52,225 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-07 16:33:52,225 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-07 16:33:52,226 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-07 16:33:52,226 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-07 16:33:52,227 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-07 16:33:52,227 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-07 16:33:52,227 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-07 16:33:52,228 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-07 16:33:52,228 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-07 16:33:52,228 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-07 16:33:52,229 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-07 16:33:52,229 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-07-07 16:33:52,229 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
2025-07-07 16:33:52,229 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-07-07 16:33:53,339 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
2025-07-07 16:33:53,339 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-07-07 16:33:53,339 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-07-07 16:33:53,340 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-07 16:33:53,342 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-07 16:33:53,342 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-07 16:33:53,380 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-07 16:33:53,815 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-07 16:33:53,817 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-07 16:33:53,818 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-07 16:33:53,818 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-07 16:33:53,818 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-07 16:33:53,818 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-07 16:33:53,819 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-07 16:33:53,819 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-07 16:33:53,819 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-07 16:33:53,819 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-07 16:33:53,819 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-07 16:33:53,819 - utils.database - INFO - Database initialized successfully
2025-07-07 16:33:53,819 - utils.database - INFO - Checking initial database state...
2025-07-07 16:33:53,820 - utils.database - INFO - Database state: 0 suites, 0 cases, 2011 steps, 1 screenshots, 2011 tracking entries
2025-07-07 16:33:53,840 - app - INFO - Using directories from config.py:
2025-07-07 16:33:53,840 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-07 16:33:53,840 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-07 16:33:53,840 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-07 16:33:53,849] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-07 16:33:53,849] INFO in database: Test_steps table schema updated successfully
[2025-07-07 16:33:53,849] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-07 16:33:53,850] INFO in database: Screenshots table schema updated successfully
[2025-07-07 16:33:53,850] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-07 16:33:53,850] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-07 16:33:53,851] INFO in database: action_type column already exists in execution_tracking table
[2025-07-07 16:33:53,851] INFO in database: action_params column already exists in execution_tracking table
[2025-07-07 16:33:53,851] INFO in database: action_id column already exists in execution_tracking table
[2025-07-07 16:33:53,851] INFO in database: Successfully updated execution_tracking table schema
[2025-07-07 16:33:53,851] INFO in database: Database initialized successfully
[2025-07-07 16:33:53,851] INFO in database: Checking initial database state...
[2025-07-07 16:33:53,853] INFO in database: Database state: 0 suites, 0 cases, 2011 steps, 1 screenshots, 2011 tracking entries
[2025-07-07 16:33:53,854] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-07 16:33:53,855] INFO in database: Test_steps table schema updated successfully
[2025-07-07 16:33:53,855] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-07 16:33:53,856] INFO in database: Screenshots table schema updated successfully
[2025-07-07 16:33:53,856] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-07 16:33:53,856] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-07 16:33:53,856] INFO in database: action_type column already exists in execution_tracking table
[2025-07-07 16:33:53,856] INFO in database: action_params column already exists in execution_tracking table
[2025-07-07 16:33:53,857] INFO in database: action_id column already exists in execution_tracking table
[2025-07-07 16:33:53,857] INFO in database: Successfully updated execution_tracking table schema
[2025-07-07 16:33:53,857] INFO in database: Database initialized successfully
[2025-07-07 16:33:53,857] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-07 16:33:53,857] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-07 16:33:53,858] INFO in database: action_type column already exists in execution_tracking table
[2025-07-07 16:33:53,858] INFO in database: action_params column already exists in execution_tracking table
[2025-07-07 16:33:53,858] INFO in database: action_id column already exists in execution_tracking table
[2025-07-07 16:33:53,858] INFO in database: Successfully updated execution_tracking table schema
[2025-07-07 16:33:53,858] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-07 16:33:53,859] INFO in database: Screenshots table schema updated successfully
[2025-07-07 16:33:53,987] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-07 16:33:53,999] INFO in appium_device_controller: Appium server is running and ready
[2025-07-07 16:33:54,000] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-07 16:33:54,000] INFO in appium_device_controller: Connection monitoring started
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-07 16:33:54,021] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://************:8081
[2025-07-07 16:33:54,021] INFO in _internal: [33mPress CTRL+C to quit[0m
